#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>

#define PHYSICAL_BLOCK_SIZE 1024
#define LOGICAL_BLOCK_SIZE 1024
#define NAME_LEN 14
#define START_PARTITION_TABLE 0x1be

//分区表结构
struct par_table_entry {
	char boot_indicator;	//导入指示器，绝大多数情况都是0
	char start_chs_val[3];	//起始柱面磁头扇区值，3个字节分别对应柱面号、磁头号、扇区号
	char par_type;			//分区类型
	char end_chs_val[3];	//终止柱面磁头扇区值
	int start_sector;		//起始盘块号
	int par_size;			//分区大小
};

// 超级块结构体
struct super_block
{
  unsigned short s_ninodes;		// 节点数。
  unsigned short s_nzones;		// 逻辑块数。
  unsigned short s_imap_blocks;	// i 节点位图所占用的数据块数。
  unsigned short s_zmap_blocks;	// 逻辑块位图所占用的数据块数。
  unsigned short s_firstdatazone;	// 第一个数据逻辑块号。
  unsigned short s_log_zone_size;	// log(数据块数/逻辑块)。（以2 为底）。
  unsigned long s_max_size;		// 文件最大长度。
  unsigned short s_magic;		// 文件系统魔数。
};

// i节点结构体
struct d_inode
{
  unsigned short i_mode;		// 文件类型和属性(rwx 位)。
  unsigned short i_uid;			// 用户id（文件拥有者标识符）。
  unsigned long i_size;			// 文件大小（字节数）。
  unsigned long i_time;			// 修改时间（自1970.1.1:0 算起，秒）。
  unsigned char i_gid;			// 组id(文件拥有者所在的组)。
  unsigned char i_nlinks;		// 链接数（多少个文件目录项指向该i 节点）。
  unsigned short i_zone[9];		// 直接(0-6)、间接(7)或双重间接(8)逻辑块号。
								// zone 是区的意思，可译成区段，或逻辑块。
};

//目录项结构
struct dir_entry{
	unsigned short inode;	//i节点号
	char name[NAME_LEN];	//文件名
};

struct super_block sblock;		//超级块
struct par_table_entry pte[4];	//分区表数组
FILE* fd;						//文件指针
char physical_block[PHYSICAL_BLOCK_SIZE]; //存储物理块
char logical_block[LOGICAL_BLOCK_SIZE];  //存储逻辑块
char *inode_bitmap;		//i节点位图指针
char *zone_bitmap;
//char *logical_bitmap;	//逻辑块位图指针,本实例未使用

//读取一个物理块
void get_physical_block(int block_num)
{
	//减1是因为物理盘块是从1开始计数
	fseek(fd, (block_num - 1) * PHYSICAL_BLOCK_SIZE, SEEK_SET);
	fread(physical_block, PHYSICAL_BLOCK_SIZE, 1, fd);
}

//读取第一个分区的一个逻辑块
void get_partition_logical_block(int block_num)
{
	//block_num前面加的1表示在第一个分区前还有一个主引导记录（MBR）块，
	//后面加的1是因为物理盘块是从1开始计数的,而逻辑块是从0开始计数的
	get_physical_block(1 + block_num + 1);
	memcpy(logical_block, physical_block, LOGICAL_BLOCK_SIZE);
}

//读取分区表
void get_partition_table()
{
	int i = 0;

	//分区表有4个16字节的表项组成，第一个表项的起始地址为START_PARTITION_TABLE
	get_physical_block( 1 );	//分区表在物理盘块的第1块
	memcpy(pte, &physical_block[START_PARTITION_TABLE], sizeof(pte));
	for(i = 0; i < 4; i++)
	{
		printf("**************pattition table%d****************\n", i+1);
		printf("Boot Indicator:%d\n", pte[i].boot_indicator);
		printf("start CHS value:0x%04x\n", pte[i].start_chs_val);
		printf("partition type:%ld\n", pte[i].par_type);
		printf("end CHS value:0x%04x\n", pte[i].end_chs_val);
		printf("start sector:%d\n", pte[i].start_sector);
		printf("partition size:%d\n", pte[i].par_size);
	}
}

//读取第一个分区的超级块
void get_super_block()
{	
	get_partition_logical_block( 1 );
	memcpy(&sblock, logical_block, sizeof(sblock));
	
	printf("**************super block****************\n");
	printf("ninodes：%d\n", sblock.s_ninodes);
	printf("nzones：%d\n", sblock.s_nzones);
	printf("imap_blocks：%d\n", sblock.s_imap_blocks);
	printf("zmap_blocks：%d\n", sblock.s_zmap_blocks);
	printf("firstdatazone：0x%04x\n", sblock.s_firstdatazone);
	printf("log_zone_size：%d\n", sblock.s_log_zone_size);
	printf("max_size：0x%x = %dByte\n", sblock.s_max_size,sblock.s_max_size);
	printf("magic：0x%x\n", sblock.s_magic);
}	


//加载i节点位图
void load_inode_bitmap()
{
	inode_bitmap = (char*)malloc(sblock.s_imap_blocks * LOGICAL_BLOCK_SIZE);
	int i = 0;
	for(i = 0; i < sblock.s_imap_blocks; i++)
	{
		//i节点位图前有1个引导块和一个超级块
		get_partition_logical_block(1 + 1 + i);	
		memcpy(&inode_bitmap[i * LOGICAL_BLOCK_SIZE], &logical_block, LOGICAL_BLOCK_SIZE);
	}
}

//根据i节点位图判断其对应的i节点是否有效
//参数inode_id为i节点的id
//有效返回1，无效返回0
int is_inode_valid(unsigned short inode_id)
{
	if(inode_id > sblock.s_ninodes)
		return 0;
		
	char byte = inode_bitmap[(inode_id - 1) / 8]; //inode_id减1是因为i节点是从1开始计数的
	return (byte >> (7 - (inode_id - 1) % 8) ) & 0x1;	//取一个字节中的某位与1做位运算
}

//根据i节点id读取i节点
void get_inode(unsigned short inode_id, struct d_inode* inode)
{
	//一个引导块，一个超级块，sblock.s_imap_blocks个i节点位图，sblock.s_zmap_blocks个逻辑块位图	
	//一个i节点占32个字节，一个盘块有LOGICAL_BLOCK_SIZE/32个节点，所以inode_id/(LOGICAL_BLOCK_SIZE/32)
	//减1是因为i节点号是从1开始计数的，而逻辑块号是从0开始计数的
	//inode_blocknum是i节点在逻辑块中的偏移块数
	int inode_blocknum = 1 + 1 + sblock.s_imap_blocks + sblock.s_zmap_blocks + (inode_id - 1) / (LOGICAL_BLOCK_SIZE/32) ;
	get_partition_logical_block(inode_blocknum);
	memcpy((char*)inode, &logical_block[((inode_id - 1) % sizeof(struct d_inode)) * sizeof(struct d_inode)], sizeof(struct d_inode));
}

//递归打印i节点下的目录
void print_inode(unsigned short id, int tab_count, const char* name)
{
	int i, m, n;
	struct d_inode inode;
	struct dir_entry dir;
	
	//如果i节点号对应在i节点位图相应位的值为1,说明此i节点已使用
	//否则说明此i节点无用或已被删除，则直接返回
	if(is_inode_valid(id) != 1)
		return;
		
	get_inode(id, &inode);
	tab_count++;
	unsigned short mode = inode.i_mode >> 12; //高4位存放的是文件类型
	
	//如果是目录文件
	if(mode == 4)
	{
		//打印tab键，为了使目录有层次感
		for(i=0; i<tab_count; i++)
		{
			printf("\t");
		}
		printf("%s\n", name);
		
		//循环读取i节点中的i_zones[]数组
		for(m = 0; m<7; m++)
		{
			//如果数组数据为0，则跳过
			if(inode.i_zone[m] == 0)
				continue;
			
			//一个逻辑块最多存储64个目录项,循环读取64个目录项
			//其中前两项分别为 . 和 .. 
			for(n = 0; n < 64; n++)
			{
				get_partition_logical_block(inode.i_zone[m]);
				//将逻辑块中的数据拷贝到目录项结构体
				memcpy((char*)&dir, &logical_block[n * sizeof(dir)], sizeof(dir));
				
				//如果是 .和..则继续循环
				if(n == 0 || n == 1)
					continue;
					
				//如果目录项中没有内容了则不再读取
				if(dir.inode == 0)
					break;
				
				//递归打印子目录
				print_inode(dir.inode, tab_count, dir.name);
			}
		}
	}
	
	//如果是常规文件
	else if(mode == 8)
	{
		for(i=0; i<tab_count; i++)
		{
			printf("\t");
		}
		printf("%s\n", name);
	}
	//如果块设备文件、字符设备文件等其他类型文件，请读者尝试自己实现
}
void load_zone_bitmap()
{
    zone_bitmap = (char*)malloc(sblock.s_zmap_blocks * LOGICAL_BLOCK_SIZE);
    int i = 0;
    for(i = 0; i < sblock.s_zmap_blocks; i++)
    {
        get_partition_logical_block(1 + 1 + sblock.s_imap_blocks + i);
        memcpy(&zone_bitmap[i * LOGICAL_BLOCK_SIZE], &logical_block, LOGICAL_BLOCK_SIZE);
    }
}

int count_free_bits(char* bitmap, int size)
{
    int count = 0;
    for(int i = 0; i < size; i++)
    {
        char byte = bitmap[i];
        for(int j = 0; j < 8; j++)
        {
            if((byte & (1 << j)) == 0)
                count++;
        }
    }
    return count;
}

void print_df()
{
    load_zone_bitmap();
    int free_zones = count_free_bits(zone_bitmap, sblock.s_zmap_blocks * LOGICAL_BLOCK_SIZE);
    int used_zones = sblock.s_nzones - free_zones;
    int free_inodes = count_free_bits(inode_bitmap, sblock.s_imap_blocks * LOGICAL_BLOCK_SIZE);
    int used_inodes = sblock.s_ninodes - free_inodes;
    
    printf("file_info:\n");
    printf("total: %d\n", sblock.s_nzones);
    printf("used: %d\n", used_zones);
    printf("free: %d\n", free_zones);
    printf("total_inode: %d\n", sblock.s_ninodes);
    printf("used_inode: %d\n", used_inodes);
    printf("free_inode: %d\n", free_inodes);
}

unsigned short find_inode_by_path(const char* path)
{
    if (path[0] == '\0' || strcmp(path, "/") == 0)
        return 1; // 根目录 i 节点为 1

    struct d_inode inode;
    struct dir_entry dir;
    unsigned short current_inode = 1; // 从根目录开始
    char temp_path[256];
    strcpy(temp_path, path);
    char* token = strtok(temp_path, "/");

    while (token != NULL)
    {
        get_inode(current_inode, &inode);
        if ((inode.i_mode >> 12) != 4) // 不是目录
        {
            printf("路径错误，非目录: inode=%d\n", current_inode);
            return 0;
        }

        int found = 0;
        printf("查找路径组件: '%s' 在 inode %d 中\n", token, current_inode);
        printf("  目录 inode %d 的数据块: ", current_inode);
        for (int k = 0; k < 7; k++) {
            if (inode.i_zone[k] != 0) {
                printf("%d ", inode.i_zone[k]);
            }
        }
        printf("\n");

        for (int i = 0; i < 7 && inode.i_zone[i] != 0; i++)
        {
            printf("  检查数据块 %d:\n", inode.i_zone[i]);
            get_partition_logical_block(inode.i_zone[i]);
            for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++)
            {
                memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
                if (dir.inode != 0) {
                    printf("    找到目录项: '%s' (inode=%d)\n", dir.name, dir.inode);
                    if (strcmp(dir.name, token) == 0) {
                        current_inode = dir.inode;
                        found = 1;
                        printf("    匹配成功！\n");
                        break;
                    }
                }
            }
            if (found)
                break;
        }
        if (!found)
        {
            printf("未找到路径组件: %s\n", token);
            return 0;
        }
        token = strtok(NULL, "/");
    }
    return current_inode;
}

void print_file_content(const char* path)
{
    unsigned short inode_id = find_inode_by_path(path);
    if(inode_id == 0)
    {
        printf("file unfind: %s\n", path);
        return;
    }
    struct d_inode inode;
    get_inode(inode_id, &inode);
    if((inode.i_mode >> 12) != 8) // 不是常规文件
    {
        printf("%s is not file\n", path);
        return;
    }
    printf("file content (%s):\n", path);
    for(int i = 0; i < 7; i++)
    {
        if(inode.i_zone[i] == 0)
            continue;
        get_partition_logical_block(inode.i_zone[i]);
        printf("%s", logical_block);
    }
    printf("\n");
}
void print_large_file_content(const char* path)
{
    unsigned short inode_id = find_inode_by_path(path);
    if(inode_id == 0)
    {
        printf("file unfind: %s\n", path);
        return;
    }
    struct d_inode inode;
    get_inode(inode_id, &inode);
    if((inode.i_mode >> 12) != 8)
    {
        printf("%s is not file\n", path);
        return;
    }
    printf("file content (%s):\n", path);
    // 直接块
    for(int i = 0; i < 7; i++)
    {
        if(inode.i_zone[i] == 0)
            continue;
        get_partition_logical_block(inode.i_zone[i]);
        printf("%s", logical_block);
    }
    // 间接块
    if(inode.i_zone[7] != 0)
    {
        get_partition_logical_block(inode.i_zone[7]);
        unsigned short* indirect = (unsigned short*)logical_block;
        for(int i = 0; i < LOGICAL_BLOCK_SIZE / 2; i++)
        {
            if(indirect[i] == 0)
                continue;
            get_partition_logical_block(indirect[i]);
            printf("%s", logical_block);
        }
    }
    // 双重间接块
    if(inode.i_zone[8] != 0)
    {
        get_partition_logical_block(inode.i_zone[8]);
        unsigned short* double_indirect = (unsigned short*)logical_block;
        for(int i = 0; i < LOGICAL_BLOCK_SIZE / 2; i++)
        {
            if(double_indirect[i] == 0)
                continue;
            get_partition_logical_block(double_indirect[i]);
            unsigned short* indirect = (unsigned short*)logical_block;
            for(int j = 0; j < LOGICAL_BLOCK_SIZE / 2; j++)
            {
                if(indirect[j] == 0)
                    continue;
                get_partition_logical_block(indirect[j]);
                printf("%s", logical_block);
            }
        }
    }
    printf("\n");
}
void delete_file(const char* path)
{
    printf("开始删除文件: %s\n", path);

    // 提取父目录路径
    char parent_path[256];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) // 防止操作根目录
    {
        printf("不能删除根目录或根目录下的文件: %s\n", path);
        return;
    }
    *last_slash = '\0';
    printf("父目录路径: %s\n", parent_path);

    // 找到父目录的 i 节点
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0)
    {
        printf("父目录未找到: %s\n", parent_path);
        return;
    }
    printf("父目录 i 节点: %d\n", parent_inode_id);

    // 找到文件的 i 节点
    unsigned short file_inode_id = find_inode_by_path(path);
    if (file_inode_id == 0)
    {
        printf("文件未找到: %s\n", path);
        return;
    }
    printf("文件 i 节点: %d\n", file_inode_id);

    if (file_inode_id == 1) // 防止删除根 i 节点
    {
        printf("错误: 试图删除根 i 节点！\n");
        return;
    }

    // 检查文件类型
    struct d_inode file_inode;
    get_inode(file_inode_id, &file_inode);
    unsigned short file_mode = file_inode.i_mode >> 12;
    printf("文件类型: %d (8=普通文件, 4=目录)\n", file_mode);
    if (file_mode != 8) {
        printf("警告: 不是普通文件，类型为 %d\n", file_mode);
    }

    // 清空父目录中的目录项
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);
    int found = 0;
    for (int i = 0; i < 7 && !found; i++)
    {
        if (parent_inode.i_zone[i] == 0)
            continue;
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++)
        {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == file_inode_id)
            {
                dir.inode = 0; // 清空目录项的 i 节点号
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));
                // 写回逻辑块
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                printf("写回父目录数据块: offset=%ld\n", offset);
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
    }

    // 释放 i 节点
    int byte_index = (file_inode_id - 1) / 8;
    int bit_index = (file_inode_id - 1) % 8;
    printf("释放 i 节点: id=%d, byte=%d, bit=%d\n", file_inode_id, byte_index, bit_index);
    inode_bitmap[byte_index] &= ~(1 << (7 - bit_index));
    // 写回 i 节点位图
    for (int i = 0; i < sblock.s_imap_blocks; i++)
    {
        int logical_block_num = 2 + i; // i 节点位图从逻辑块 2 开始
        int physical_block_num = 2 + logical_block_num; // 映射到物理块
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE; // 正确偏移量
        memcpy(logical_block, &inode_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }

    // 释放数据块 - 重用之前声明的file_inode变量
    get_inode(file_inode_id, &file_inode);
    for (int i = 0; i < 7; i++)
    {
        if (file_inode.i_zone[i] != 0)
        {
            int zone_id = file_inode.i_zone[i];
            int byte_index = (zone_id - sblock.s_firstdatazone) / 8;
            int bit_index = (zone_id - sblock.s_firstdatazone) % 8;
            printf("释放数据块: zone=%d, byte=%d, bit=%d\n", zone_id, byte_index, bit_index);
            zone_bitmap[byte_index] &= ~(1 << (7 - bit_index));
        }
    }
    // 写回逻辑块位图
    for (int i = 0; i < sblock.s_zmap_blocks; i++)
    {
        int logical_block_num = 1 + 1 + sblock.s_imap_blocks + i; // 逻辑块位图的逻辑块号
        int physical_block_num = 1 + logical_block_num; // 映射到物理块号
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE; // 物理偏移
        memcpy(logical_block, &zone_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        printf("写回逻辑块位图: logical_block=%d, physical_block=%d, offset=%ld\n",
               logical_block_num, physical_block_num, offset);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }

    printf("文件已删除: %s\n", path);
}
void delete_directory(const char* path) {
    printf("开始删除目录: %s\n", path);

    unsigned short dir_inode_id = find_inode_by_path(path);
    if (dir_inode_id == 0) {
        printf("文件夹不存在: %s\n", path);
        return;
    }
    printf("找到目录 inode: %d\n", dir_inode_id);

    struct d_inode dir_inode;
    get_inode(dir_inode_id, &dir_inode);
    if ((dir_inode.i_mode >> 12) != 4) {
        printf("%s 不是文件夹，类型为: %d\n", path, (dir_inode.i_mode >> 12));
        return;
    }

    // 递归删除目录中的所有内容
    printf("删除目录内容...\n");
    for (int i = 0; i < 7; i++) {
        if (dir_inode.i_zone[i] != 0) {
            printf("检查数据块 %d\n", dir_inode.i_zone[i]);
            get_partition_logical_block(dir_inode.i_zone[i]);
            for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
                struct dir_entry dir;
                memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
                if (dir.inode != 0 && strcmp(dir.name, ".") != 0 && strcmp(dir.name, "..") != 0) {
                    printf("找到子项: %s (inode=%d)\n", dir.name, dir.inode);
                    char sub_path[256];
                    snprintf(sub_path, sizeof(sub_path), "%s/%s", path, dir.name);
                    struct d_inode sub_inode;
                    get_inode(dir.inode, &sub_inode);
                    if ((sub_inode.i_mode >> 12) == 4) {
                        printf("递归删除子目录: %s\n", sub_path);
                        delete_directory(sub_path);
                    } else {
                        printf("删除子文件: %s\n", sub_path);
                        delete_file(sub_path);
                    }
                }
            }
        }
    }

    // 现在删除目录本身
    printf("删除目录本身: %s\n", path);

    // 提取父目录路径
    char parent_path[256];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) {
        printf("不能删除根目录: %s\n", path);
        return;
    }
    *last_slash = '\0';

    // 找到父目录的 i 节点
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0) {
        printf("父目录未找到: %s\n", parent_path);
        return;
    }

    // 从父目录中删除目录项
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);

    char* dir_name = last_slash + 1;
    int found = 0;

    for (int i = 0; i < 7 && parent_inode.i_zone[i] != 0; i++) {
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == dir_inode_id && strcmp(dir.name, dir_name) == 0) {
                printf("清除父目录中的目录项: %s\n", dir_name);
                memset(&dir, 0, sizeof(struct dir_entry));
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));

                // 写回父目录数据块
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
        if (found) break;
    }

    // 释放目录的 i 节点
    printf("释放目录 i 节点: %d\n", dir_inode_id);
    int byte_index = (dir_inode_id - 1) / 8;
    int bit_index = (dir_inode_id - 1) % 8;
    inode_bitmap[byte_index] &= ~(1 << (7 - bit_index));

    // 释放目录的数据块
    for (int i = 0; i < 7; i++) {
        if (dir_inode.i_zone[i] != 0) {
            printf("释放数据块: %d\n", dir_inode.i_zone[i]);
            int zone_index = dir_inode.i_zone[i] - sblock.s_firstdatazone;
            int zone_byte = zone_index / 8;
            int zone_bit = zone_index % 8;
            zone_bitmap[zone_byte] &= ~(1 << (7 - zone_bit));
        }
    }

    // 写回 i 节点位图
    for (int i = 0; i < sblock.s_imap_blocks; i++) {
        int logical_block_num = 2 + i;
        int physical_block_num = 2 + logical_block_num;
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
        memcpy(logical_block, &inode_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }

    // 写回逻辑块位图
    for (int i = 0; i < sblock.s_zmap_blocks; i++) {
        int logical_block_num = 1 + 1 + sblock.s_imap_blocks + i;
        int physical_block_num = 1 + logical_block_num;
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
        memcpy(logical_block, &zone_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }

    printf("文件夹已删除: %s\n", path);
}

unsigned short allocate_inode() {
    for (int i = 0; i < sblock.s_ninodes; i++) {
        int byte_index = i / 8;
        int bit_index = i % 8;
        if ((inode_bitmap[byte_index] & (1 << (7 - bit_index))) == 0) {
            inode_bitmap[byte_index] |= (1 << (7 - bit_index));
            for (int j = 0; j < sblock.s_imap_blocks; j++) {
                int logical_block_num = 2 + j;
                int physical_block_num = 2 + logical_block_num;
                long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
                memcpy(logical_block, &inode_bitmap[j * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
            }
            return i + 1;
        }
    }
    return 0;
}

unsigned short allocate_zone() {
    for (int i = 0; i < sblock.s_nzones - sblock.s_firstdatazone; i++) {
        int byte_index = i / 8;
        int bit_index = i % 8;
        if ((zone_bitmap[byte_index] & (1 << (7 - bit_index))) == 0) {
            zone_bitmap[byte_index] |= (1 << (7 - bit_index));
            for (int j = 0; j < sblock.s_zmap_blocks; j++) {
                get_partition_logical_block(1 + 1 + sblock.s_imap_blocks + j);
                memcpy(logical_block, &zone_bitmap[j * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
                long offset = (1 + 1 + sblock.s_imap_blocks + j) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
            }
            return sblock.s_firstdatazone + i;
        }
    }
    return 0;
}

void write_inode(unsigned short inode_id, struct d_inode* inode) {
    int inode_blocknum = 1 + 1 + sblock.s_imap_blocks + sblock.s_zmap_blocks + (inode_id - 1) / (LOGICAL_BLOCK_SIZE / sizeof(struct d_inode));
    get_partition_logical_block(inode_blocknum);
    memcpy(&logical_block[((inode_id - 1) % (LOGICAL_BLOCK_SIZE / sizeof(struct d_inode))) * sizeof(struct d_inode)], inode, sizeof(struct d_inode));
    long offset = (1 + inode_blocknum) * LOGICAL_BLOCK_SIZE;
    fseek(fd, offset, SEEK_SET);
    fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
}

void create_directory(const char* path) {
    char parent_path[256], dir_name[NAME_LEN];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) {
        printf("无效路径: %s\n", path);
        return;
    }
    strncpy(dir_name, last_slash + 1, NAME_LEN);
    dir_name[NAME_LEN - 1] = '\0';
    *last_slash = '\0';
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0) {
        printf("父目录不存在: %s\n", parent_path);
        return;
    }
    if (find_inode_by_path(path) != 0) {
        printf("目录已存在: %s\n", path);
        return;
    }
    unsigned short new_inode_id = allocate_inode();
    if (new_inode_id == 0) {
        printf("没有空闲 i 节点\n");
        return;
    }
    struct d_inode new_inode;
    memset(&new_inode, 0, sizeof(new_inode));
    new_inode.i_mode = (4 << 12) | 0755;
    new_inode.i_uid = 0;
    new_inode.i_gid = 0;
    new_inode.i_nlinks = 2;
    new_inode.i_time = time(NULL);
    unsigned short data_block = allocate_zone();
    if (data_block == 0) {
        printf("没有空闲数据块\n");
        return;
    }
    new_inode.i_zone[0] = data_block;
    write_inode(new_inode_id, &new_inode);
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);
    int found = 0;
    for (int i = 0; i < 7 && !found; i++) {
        if (parent_inode.i_zone[i] == 0) {
            parent_inode.i_zone[i] = allocate_zone();
            if (parent_inode.i_zone[i] == 0) {
                printf("父目录无法分配数据块\n");
                return;
            }
            write_inode(parent_inode_id, &parent_inode);
        }
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == 0) {
                dir.inode = new_inode_id;
                strncpy(dir.name, dir_name, NAME_LEN);
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
    }
    if (!found) {
        printf("父目录没有空闲目录项\n");
        return;
    }
    struct dir_entry dot = {new_inode_id, "."};
    struct dir_entry dotdot = {parent_inode_id, ".."};
    get_partition_logical_block(data_block);
    memcpy(&logical_block[0], &dot, sizeof(struct dir_entry));
    memcpy(&logical_block[sizeof(struct dir_entry)], &dotdot, sizeof(struct dir_entry));
    long offset = (1 + data_block) * LOGICAL_BLOCK_SIZE;
    fseek(fd, offset, SEEK_SET);
    fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    printf("目录已创建: %s\n", path);
}

void create_file(const char* path, unsigned long size) {
    char parent_path[256], file_name[NAME_LEN];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) {
        printf("无效路径: %s\n", path);
        return;
    }
    strncpy(file_name, last_slash + 1, NAME_LEN);
    file_name[NAME_LEN - 1] = '\0';
    *last_slash = '\0';
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0) {
        printf("父目录不存在: %s\n", parent_path);
        return;
    }
    if (find_inode_by_path(path) != 0) {
        printf("文件已存在: %s\n", path);
        return;
    }
    unsigned short new_inode_id = allocate_inode();
    if (new_inode_id == 0) {
        printf("没有空闲 i 节点\n");
        return;
    }
    struct d_inode new_inode;
    memset(&new_inode, 0, sizeof(new_inode));
    new_inode.i_mode = (8 << 12) | 0644;
    new_inode.i_uid = 0;
    new_inode.i_gid = 0;
    new_inode.i_nlinks = 1;
    new_inode.i_time = time(NULL);
    new_inode.i_size = size;
    int blocks_needed = (size + LOGICAL_BLOCK_SIZE - 1) / LOGICAL_BLOCK_SIZE;
    if (blocks_needed > 7) {
        printf("文件大小超出直接块支持范围: %lu\n", size);
        return;
    }
    for (int i = 0; i < blocks_needed; i++) {
        unsigned short data_block = allocate_zone();
        if (data_block == 0) {
            printf("没有空闲数据块\n");
            return;
        }
        new_inode.i_zone[i] = data_block;
        memset(logical_block, 0, LOGICAL_BLOCK_SIZE);
        long offset = (1 + data_block) * LOGICAL_BLOCK_SIZE;
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }
    write_inode(new_inode_id, &new_inode);
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);
    int found = 0;
    for (int i = 0; i < 7 && !found; i++) {
        if (parent_inode.i_zone[i] == 0) {
            parent_inode.i_zone[i] = allocate_zone();
            if (parent_inode.i_zone[i] == 0) {
                printf("父目录无法分配数据块\n");
                return;
            }
            write_inode(parent_inode_id, &parent_inode);
        }
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == 0) {
                dir.inode = new_inode_id;
                strncpy(dir.name, file_name, NAME_LEN);
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
    }
    if (!found) {
        printf("父目录没有空闲目录项\n");
        return;
    }
    printf("文件已创建: %s, 大小: %lu\n", path, size);
}
int main(int argc, char* argv[])
{
	int bit;
	struct d_inode* inode = (struct d_inode*)malloc(sizeof(struct d_inode));
	
	char* path = "D:\\minix\\harddisk.img";
	fd = fopen(path, "rb+");
	if(fd==NULL)
		printf("open file failed!\n");
		
	//读取分区表
	get_partition_table();
	//读取超级块
	get_super_block();
	
	//加载i节点逻辑块位图
	load_inode_bitmap();
	load_zone_bitmap();

    if (!is_inode_valid(1))
    {
        printf("根 i 节点无效！请检查文件系统完整性。\n");
        return 1;
    }
    
	print_df();

	// 首先显示当前的目录结构
	printf("=== 当前文件系统状态 ===\n");
	printf("根目录内容：\n");
	struct d_inode root_inode;
	get_inode(1, &root_inode);

	for (int i = 0; i < 7 && root_inode.i_zone[i] != 0; i++) {
	    printf("检查根目录数据块 %d:\n", root_inode.i_zone[i]);
	    get_partition_logical_block(root_inode.i_zone[i]);
	    for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
	        struct dir_entry dir;
	        memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
	        if (dir.inode != 0) {
	            printf("  - %s (inode=%d)\n", dir.name, dir.inode);
	        }
	    }
	}

	// 测试创建目录功能
	printf("\n=== 测试创建目录功能：创建 /usr/root/dir 目录 ===\n");

	// 首先检查 /usr/root 目录是否存在
	printf("1. 检查父目录 /usr/root 是否存在\n");
	unsigned short parent_inode = find_inode_by_path("/usr/root");
	if (parent_inode != 0) {
	    printf("找到父目录 /usr/root，inode = %d\n", parent_inode);

	    // 检查目标目录是否已存在
	    printf("2. 检查目标目录 /usr/root/dir 是否已存在\n");
	    unsigned short target_inode = find_inode_by_path("/usr/root/dir");
	    if (target_inode != 0) {
	        printf("目录 /usr/root/dir 已存在，inode = %d\n", target_inode);
	    } else {
	        printf("目录 /usr/root/dir 不存在，开始创建\n");

	        // 创建目录
	        printf("3. 创建目录 /usr/root/dir\n");
	        create_directory("/usr/root/dir");

	        // 验证创建是否成功
	        printf("4. 验证目录创建是否成功\n");
	        target_inode = find_inode_by_path("/usr/root/dir");
	        if (target_inode != 0) {
	            printf("✓ 目录 /usr/root/dir 创建成功！inode = %d\n", target_inode);

	            // 显示新创建目录的详细信息
	            struct d_inode dir_inode;
	            get_inode(target_inode, &dir_inode);
	            printf("目录详细信息：\n");
	            printf("  - 类型：%d (4=目录)\n", (dir_inode.i_mode >> 12));
	            printf("  - 权限：%o\n", dir_inode.i_mode & 0777);
	            printf("  - 链接数：%d\n", dir_inode.i_nlinks);
	            printf("  - 数据块：%d\n", dir_inode.i_zone[0]);
	        } else {
	            printf("✗ 目录 /usr/root/dir 创建失败！\n");
	        }
	    }
	} else {
	    printf("父目录 /usr/root 不存在\n");
	    printf("尝试查找其他可能的路径...\n");

	    // 尝试在根目录下创建测试目录
	    printf("在根目录下创建测试目录 /test_dir\n");
	    unsigned short root_inode = find_inode_by_path("/");
	    if (root_inode != 0) {
	        printf("找到根目录，inode = %d\n", root_inode);
	        create_directory("/test_dir");

	        unsigned short test_inode = find_inode_by_path("/test_dir");
	        if (test_inode != 0) {
	            printf("✓ 测试目录 /test_dir 创建成功！inode = %d\n", test_inode);
	        } else {
	            printf("✗ 测试目录 /test_dir 创建失败！\n");
	        }
	    }
	}

	printf("\n=== 创建目录功能说明 ===\n");
	printf("create_directory 函数的主要步骤：\n");
	printf("1. 解析路径，分离父目录和目录名\n");
	printf("2. 检查父目录是否存在\n");
	printf("3. 检查目标目录是否已存在\n");
	printf("4. 分配新的 i 节点和数据块\n");
	printf("5. 初始化目录 i 节点（类型=4，权限=755）\n");
	printf("6. 在父目录中添加新目录项\n");
	printf("7. 在新目录中创建 '.' 和 '..' 目录项\n");
	printf("8. 将所有更改写回磁盘\n");
	//i节点位图的第一位对应文件系统的根节点
	//如果第一位为1，则打印根节点
	bit = is_inode_valid(1);
	if(bit == 1)
		print_inode(1, -1, "\\");
	else
		printf("root node lost！\n");
	
	return 0;
}
