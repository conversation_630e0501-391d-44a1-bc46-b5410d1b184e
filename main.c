#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>

#define PHYSICAL_BLOCK_SIZE 1024
#define LOGICAL_BLOCK_SIZE 1024
#define NAME_LEN 14
#define START_PARTITION_TABLE 0x1be

//分区表结构
struct par_table_entry {
	char boot_indicator;	//导入指示器，绝大多数情况都是0
	char start_chs_val[3];	//起始柱面磁头扇区值，3个字节分别对应柱面号、磁头号、扇区号
	char par_type;			//分区类型
	char end_chs_val[3];	//终止柱面磁头扇区值
	int start_sector;		//起始盘块号
	int par_size;			//分区大小
};

// 超级块结构体
struct super_block
{
  unsigned short s_ninodes;		// 节点数。
  unsigned short s_nzones;		// 逻辑块数。
  unsigned short s_imap_blocks;	// i 节点位图所占用的数据块数。
  unsigned short s_zmap_blocks;	// 逻辑块位图所占用的数据块数。
  unsigned short s_firstdatazone;	// 第一个数据逻辑块号。
  unsigned short s_log_zone_size;	// log(数据块数/逻辑块)。（以2 为底）。
  unsigned long s_max_size;		// 文件最大长度。
  unsigned short s_magic;		// 文件系统魔数。
};

// i节点结构体
struct d_inode
{
  unsigned short i_mode;		// 文件类型和属性(rwx 位)。
  unsigned short i_uid;			// 用户id（文件拥有者标识符）。
  unsigned long i_size;			// 文件大小（字节数）。
  unsigned long i_time;			// 修改时间（自1970.1.1:0 算起，秒）。
  unsigned char i_gid;			// 组id(文件拥有者所在的组)。
  unsigned char i_nlinks;		// 链接数（多少个文件目录项指向该i 节点）。
  unsigned short i_zone[9];		// 直接(0-6)、间接(7)或双重间接(8)逻辑块号。
								// zone 是区的意思，可译成区段，或逻辑块。
};

//目录项结构
struct dir_entry{
	unsigned short inode;	//i节点号
	char name[NAME_LEN];	//文件名
};

struct super_block sblock;		//超级块
struct par_table_entry pte[4];	//分区表数组
FILE* fd;						//文件指针
char physical_block[PHYSICAL_BLOCK_SIZE]; //存储物理块
char logical_block[LOGICAL_BLOCK_SIZE];  //存储逻辑块
char *inode_bitmap;		//i节点位图指针
char *zone_bitmap;
//char *logical_bitmap;	//逻辑块位图指针,本实例未使用

//读取一个物理块
void get_physical_block(int block_num)
{
	//减1是因为物理盘块是从1开始计数
	fseek(fd, (block_num - 1) * PHYSICAL_BLOCK_SIZE, SEEK_SET);
	fread(physical_block, PHYSICAL_BLOCK_SIZE, 1, fd);
}

//读取第一个分区的一个逻辑块
void get_partition_logical_block(int block_num)
{
	//block_num前面加的1表示在第一个分区前还有一个主引导记录（MBR）块，
	//后面加的1是因为物理盘块是从1开始计数的,而逻辑块是从0开始计数的
	get_physical_block(1 + block_num + 1);
	memcpy(logical_block, physical_block, LOGICAL_BLOCK_SIZE);
}

//读取分区表
void get_partition_table()
{
	int i = 0;

	//分区表有4个16字节的表项组成，第一个表项的起始地址为START_PARTITION_TABLE
	get_physical_block( 1 );	//分区表在物理盘块的第1块
	memcpy(pte, &physical_block[START_PARTITION_TABLE], sizeof(pte));
	for(i = 0; i < 4; i++)
	{
		printf("**************pattition table%d****************\n", i+1);
		printf("Boot Indicator:%d\n", pte[i].boot_indicator);
		printf("start CHS value:0x%04x\n", pte[i].start_chs_val);
		printf("partition type:%ld\n", pte[i].par_type);
		printf("end CHS value:0x%04x\n", pte[i].end_chs_val);
		printf("start sector:%d\n", pte[i].start_sector);
		printf("partition size:%d\n", pte[i].par_size);
	}
}

//读取第一个分区的超级块
void get_super_block()
{	
	get_partition_logical_block( 1 );
	memcpy(&sblock, logical_block, sizeof(sblock));
	
	printf("**************super block****************\n");
	printf("ninodes：%d\n", sblock.s_ninodes);
	printf("nzones：%d\n", sblock.s_nzones);
	printf("imap_blocks：%d\n", sblock.s_imap_blocks);
	printf("zmap_blocks：%d\n", sblock.s_zmap_blocks);
	printf("firstdatazone：0x%04x\n", sblock.s_firstdatazone);
	printf("log_zone_size：%d\n", sblock.s_log_zone_size);
	printf("max_size：0x%x = %dByte\n", sblock.s_max_size,sblock.s_max_size);
	printf("magic：0x%x\n", sblock.s_magic);
}	


//加载i节点位图
void load_inode_bitmap()
{
	inode_bitmap = (char*)malloc(sblock.s_imap_blocks * LOGICAL_BLOCK_SIZE);
	int i = 0;
	for(i = 0; i < sblock.s_imap_blocks; i++)
	{
		//i节点位图前有1个引导块和一个超级块
		get_partition_logical_block(1 + 1 + i);	
		memcpy(&inode_bitmap[i * LOGICAL_BLOCK_SIZE], &logical_block, LOGICAL_BLOCK_SIZE);
	}
}

//根据i节点位图判断其对应的i节点是否有效
//参数inode_id为i节点的id
//有效返回1，无效返回0
int is_inode_valid(unsigned short inode_id)
{
	if(inode_id > sblock.s_ninodes)
		return 0;
		
	char byte = inode_bitmap[(inode_id - 1) / 8]; //inode_id减1是因为i节点是从1开始计数的
	return (byte >> (7 - (inode_id - 1) % 8) ) & 0x1;	//取一个字节中的某位与1做位运算
}

//根据i节点id读取i节点
void get_inode(unsigned short inode_id, struct d_inode* inode)
{
	//一个引导块，一个超级块，sblock.s_imap_blocks个i节点位图，sblock.s_zmap_blocks个逻辑块位图	
	//一个i节点占32个字节，一个盘块有LOGICAL_BLOCK_SIZE/32个节点，所以inode_id/(LOGICAL_BLOCK_SIZE/32)
	//减1是因为i节点号是从1开始计数的，而逻辑块号是从0开始计数的
	//inode_blocknum是i节点在逻辑块中的偏移块数
	int inode_blocknum = 1 + 1 + sblock.s_imap_blocks + sblock.s_zmap_blocks + (inode_id - 1) / (LOGICAL_BLOCK_SIZE/32) ;
	get_partition_logical_block(inode_blocknum);
	memcpy((char*)inode, &logical_block[((inode_id - 1) % sizeof(struct d_inode)) * sizeof(struct d_inode)], sizeof(struct d_inode));
}

//递归打印i节点下的目录
void print_inode(unsigned short id, int tab_count, const char* name)
{
	int i, m, n;
	struct d_inode inode;
	struct dir_entry dir;
	
	//如果i节点号对应在i节点位图相应位的值为1,说明此i节点已使用
	//否则说明此i节点无用或已被删除，则直接返回
	if(is_inode_valid(id) != 1)
		return;
		
	get_inode(id, &inode);
	tab_count++;
	unsigned short mode = inode.i_mode >> 12; //高4位存放的是文件类型
	
	//如果是目录文件
	if(mode == 4)
	{
		//打印tab键，为了使目录有层次感
		for(i=0; i<tab_count; i++)
		{
			printf("\t");
		}
		printf("%s\n", name);
		
		//循环读取i节点中的i_zones[]数组
		for(m = 0; m<7; m++)
		{
			//如果数组数据为0，则跳过
			if(inode.i_zone[m] == 0)
				continue;
			
			//一个逻辑块最多存储64个目录项,循环读取64个目录项
			//其中前两项分别为 . 和 .. 
			for(n = 0; n < 64; n++)
			{
				get_partition_logical_block(inode.i_zone[m]);
				//将逻辑块中的数据拷贝到目录项结构体
				memcpy((char*)&dir, &logical_block[n * sizeof(dir)], sizeof(dir));
				
				//如果是 .和..则继续循环
				if(n == 0 || n == 1)
					continue;
					
				//如果目录项中没有内容了则不再读取
				if(dir.inode == 0)
					break;
				
				//递归打印子目录
				print_inode(dir.inode, tab_count, dir.name);
			}
		}
	}
	
	//如果是常规文件
	else if(mode == 8)
	{
		for(i=0; i<tab_count; i++)
		{
			printf("\t");
		}
		printf("%s\n", name);
	}
	//如果块设备文件、字符设备文件等其他类型文件，请读者尝试自己实现
}
//加载区域位图
void load_zone_bitmap()
{
    zone_bitmap = (char*)malloc(sblock.s_zmap_blocks * LOGICAL_BLOCK_SIZE);
    int i = 0;
    for(i = 0; i < sblock.s_zmap_blocks; i++)
    {
        get_partition_logical_block(1 + 1 + sblock.s_imap_blocks + i);
        memcpy(&zone_bitmap[i * LOGICAL_BLOCK_SIZE], &logical_block, LOGICAL_BLOCK_SIZE);
    }
}
//打印文件系统的磁盘使用情况统计信息
int count_free_bits(char* bitmap, int size)
{
    int count = 0;
    for(int i = 0; i < size; i++)
    {
        char byte = bitmap[i];
        for(int j = 0; j < 8; j++)
        {
            if((byte & (1 << j)) == 0)
                count++;
        }
    }
    return count;
}
//根据文件路径查找对应的inode编号
void print_df()
{
    load_zone_bitmap();
    int free_zones = count_free_bits(zone_bitmap, sblock.s_zmap_blocks * LOGICAL_BLOCK_SIZE);
    int used_zones = sblock.s_nzones - free_zones;
    int free_inodes = count_free_bits(inode_bitmap, sblock.s_imap_blocks * LOGICAL_BLOCK_SIZE);
    int used_inodes = sblock.s_ninodes - free_inodes;
    printf("file_info:↓\n");
    printf("blocks total: %d\n", sblock.s_nzones);
    printf("blocks used: %d\n", used_zones);
    printf("blocks free: %d\n", free_zones);
    printf("inode total: %d\n", sblock.s_ninodes);
    printf("inode used: %d\n", used_inodes);
    printf("inode free: %d\n", free_inodes);
}
//打印指定路径的普通文件内容
unsigned short find_inode_by_path(const char* path)
{
    if (path[0] == '\0' || strcmp(path, "/") == 0)
        return 1;

    struct d_inode inode;
    struct dir_entry dir;
    unsigned short current_inode = 1;
    char temp_path[256];
    strcpy(temp_path, path);
    char* token = strtok(temp_path, "/");

    while (token != NULL)
    {
        get_inode(current_inode, &inode);
        if ((inode.i_mode >> 12) != 4)
        {
            return 0;
        }
        int found = 0;
        for (int k = 0; k < 7; k++) {
            if (inode.i_zone[k] != 0) {
                printf("%d ", inode.i_zone[k]);
            }
        }
        printf("\n");

        for (int i = 0; i < 7 && inode.i_zone[i] != 0; i++)
        {
            get_partition_logical_block(inode.i_zone[i]);
            for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++)
            {
                memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
                if (dir.inode != 0) {
                    if (strcmp(dir.name, token) == 0) {
                        current_inode = dir.inode;
                        found = 1;
                        break;
                    }
                }
            }
            if (found)
                break;
        }
        if (!found)
        {
            return 0;
        }
        token = strtok(NULL, "/");
    }
    return current_inode;
}
//打印指定路径的大文件内容，支持间接块和双重间接块
void print_file_content(const char* path)
{
    unsigned short inode_id = find_inode_by_path(path);
    if(inode_id == 0)
    {
        printf("file unfind: %s\n", path);
        return;
    }
    struct d_inode inode;
    get_inode(inode_id, &inode);
    if((inode.i_mode >> 12) != 8)
    {
        printf("%s is not file\n", path);
        return;
    }
    printf("file content (%s):\n", path);
    for(int i = 0; i < 7; i++)
    {
        if(inode.i_zone[i] == 0)
            continue;
        get_partition_logical_block(inode.i_zone[i]);
        printf("%s", logical_block);
    }
    printf("\n");
}
void print_large_file_content(const char* path)
{
    unsigned short inode_id = find_inode_by_path(path);
    if(inode_id == 0)
    {
        printf("文件未找到: %s\n", path);
        return;
    }
    struct d_inode inode;
    get_inode(inode_id, &inode);
    if((inode.i_mode >> 12) != 8)
    {
        printf("%s 不是文件\n", path);
        return;
    }

    unsigned long bytes_read = 0;
    unsigned long file_size = inode.i_size;
    char temp_block[LOGICAL_BLOCK_SIZE];

    for(int i = 0; i < 7 && bytes_read < file_size; i++)
    {
        if(inode.i_zone[i] == 0)
            continue;
        get_partition_logical_block(inode.i_zone[i]);

        unsigned long bytes_to_read = LOGICAL_BLOCK_SIZE;
        if(bytes_read + bytes_to_read > file_size)
            bytes_to_read = file_size - bytes_read;

        for(unsigned long j = 0; j < bytes_to_read; j++)
        {
            putchar(logical_block[j]);
        }
        bytes_read += bytes_to_read;
    }

    if(inode.i_zone[7] != 0 && bytes_read < file_size)
    {
        get_partition_logical_block(inode.i_zone[7]);
        memcpy(temp_block, logical_block, LOGICAL_BLOCK_SIZE);
        unsigned short* indirect = (unsigned short*)temp_block;

        for(int i = 0; i < LOGICAL_BLOCK_SIZE / 2 && bytes_read < file_size; i++)
        {
            if(indirect[i] == 0)
                continue;
            get_partition_logical_block(indirect[i]);

            unsigned long bytes_to_read = LOGICAL_BLOCK_SIZE;
            if(bytes_read + bytes_to_read > file_size)
                bytes_to_read = file_size - bytes_read;

            for(unsigned long j = 0; j < bytes_to_read; j++)
            {
                putchar(logical_block[j]);
            }
            bytes_read += bytes_to_read;
        }
    }
    if(inode.i_zone[8] != 0 && bytes_read < file_size)
    {
        get_partition_logical_block(inode.i_zone[8]);
        memcpy(temp_block, logical_block, LOGICAL_BLOCK_SIZE);
        unsigned short* double_indirect = (unsigned short*)temp_block;

        for(int i = 0; i < LOGICAL_BLOCK_SIZE / 2 && bytes_read < file_size; i++)
        {
            if(double_indirect[i] == 0)
                continue;

            get_partition_logical_block(double_indirect[i]);
            char temp_indirect[LOGICAL_BLOCK_SIZE];
            memcpy(temp_indirect, logical_block, LOGICAL_BLOCK_SIZE);
            unsigned short* indirect = (unsigned short*)temp_indirect;

            for(int j = 0; j < LOGICAL_BLOCK_SIZE / 2 && bytes_read < file_size; j++)
            {
                if(indirect[j] == 0)
                    continue;
                get_partition_logical_block(indirect[j]);

                unsigned long bytes_to_read = LOGICAL_BLOCK_SIZE;
                if(bytes_read + bytes_to_read > file_size)
                    bytes_to_read = file_size - bytes_read;

                for(unsigned long k = 0; k < bytes_to_read; k++)
                {
                    putchar(logical_block[k]);
                }
                bytes_read += bytes_to_read;
            }
        }
    }
}
//删除指定路径的文件，释放其数据块和inode，并从父目录中移除目录项
void delete_file(const char* path)
{
    printf("开始删除文件: %s\n", path);
    char parent_path[256];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path)
    {
        printf("不能删除根目录或根目录下的文件: %s\n", path);
        return;
    }
    *last_slash = '\0';
    printf("父目录路径: %s\n", parent_path);
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0)
    {
        printf("父目录未找到: %s\n", parent_path);
        return;
    }
    printf("父目录 i 节点: %d\n", parent_inode_id);
    unsigned short file_inode_id = find_inode_by_path(path);
    if (file_inode_id == 0)
    {
        printf("文件未找到: %s\n", path);
        return;
    }
    printf("文件 i 节点: %d\n", file_inode_id);

    if (file_inode_id == 1)
    {
        printf("错误: 试图删除根 i 节点！\n");
        return;
    }
    struct d_inode file_inode;
    get_inode(file_inode_id, &file_inode);
    unsigned short file_mode = file_inode.i_mode >> 12;
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);
    int found = 0;
    for (int i = 0; i < 7 && !found; i++)
    {
        if (parent_inode.i_zone[i] == 0)
            continue;
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++)
        {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == file_inode_id)
            {
                dir.inode = 0;
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
    }
    int byte_index = (file_inode_id - 1) / 8;
    int bit_index = (file_inode_id - 1) % 8;
    inode_bitmap[byte_index] &= ~(1 << (7 - bit_index));
    for (int i = 0; i < sblock.s_imap_blocks; i++)
    {
        int logical_block_num = 2 + i;
        int physical_block_num = 2 + logical_block_num;
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
        memcpy(logical_block, &inode_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }
    get_inode(file_inode_id, &file_inode);
    for (int i = 0; i < 7; i++)
    {
        if (file_inode.i_zone[i] != 0)
        {
            int zone_id = file_inode.i_zone[i];
            int byte_index = (zone_id - sblock.s_firstdatazone) / 8;
            int bit_index = (zone_id - sblock.s_firstdatazone) % 8;
            zone_bitmap[byte_index] &= ~(1 << (7 - bit_index));
        }
    }
    for (int i = 0; i < sblock.s_zmap_blocks; i++)
    {
        int logical_block_num = 1 + 1 + sblock.s_imap_blocks + i;
        int physical_block_num = 1 + logical_block_num;
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
        memcpy(logical_block, &zone_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }
    printf("文件已删除: %s\n", path);
}
//递归删除指定路径的目录及其所有内容
void delete_directory(const char* path) {
    printf("开始删除目录: %s\n", path);
    unsigned short dir_inode_id = find_inode_by_path(path);
    if (dir_inode_id == 0) {
        printf("文件夹不存在: %s\n", path);
        return;
    }
    printf("找到目录 inode: %d\n", dir_inode_id);

    struct d_inode dir_inode;
    get_inode(dir_inode_id, &dir_inode);
    if ((dir_inode.i_mode >> 12) != 4) {
        return;
    }
    for (int i = 0; i < 7; i++) {
        if (dir_inode.i_zone[i] != 0) {
            get_partition_logical_block(dir_inode.i_zone[i]);
            for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
                struct dir_entry dir;
                memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
                if (dir.inode != 0 && strcmp(dir.name, ".") != 0 && strcmp(dir.name, "..") != 0) {
                    char sub_path[256];
                    snprintf(sub_path, sizeof(sub_path), "%s/%s", path, dir.name);
                    struct d_inode sub_inode;
                    get_inode(dir.inode, &sub_inode);
                    if ((sub_inode.i_mode >> 12) == 4) {
                        delete_directory(sub_path);
                    } else {
                        delete_file(sub_path);
                    }
                }
            }
        }
    }
    char parent_path[256];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) {
        return;
    }
    *last_slash = '\0';
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0) {
        return;
    }
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);

    char* dir_name = last_slash + 1;
    int found = 0;

    for (int i = 0; i < 7 && parent_inode.i_zone[i] != 0; i++) {
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == dir_inode_id && strcmp(dir.name, dir_name) == 0) {
                memset(&dir, 0, sizeof(struct dir_entry));
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));

                // 写回父目录数据块
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
        if (found) break;
    }

    int byte_index = (dir_inode_id - 1) / 8;
    int bit_index = (dir_inode_id - 1) % 8;
    inode_bitmap[byte_index] &= ~(1 << (7 - bit_index));
    for (int i = 0; i < 7; i++) {
        if (dir_inode.i_zone[i] != 0) {
            int zone_index = dir_inode.i_zone[i] - sblock.s_firstdatazone;
            int zone_byte = zone_index / 8;
            int zone_bit = zone_index % 8;
            zone_bitmap[zone_byte] &= ~(1 << (7 - zone_bit));
        }
    }
    for (int i = 0; i < sblock.s_imap_blocks; i++) {
        int logical_block_num = 2 + i;
        int physical_block_num = 2 + logical_block_num;
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
        memcpy(logical_block, &inode_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }
    for (int i = 0; i < sblock.s_zmap_blocks; i++) {
        int logical_block_num = 1 + 1 + sblock.s_imap_blocks + i;
        int physical_block_num = 1 + logical_block_num;
        long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
        memcpy(logical_block, &zone_bitmap[i * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }

    printf("文件夹已删除: %s\n", path);
}
//从inode位图中分配一个空闲的inode并返回其编号
unsigned short allocate_inode() {
    for (int i = 0; i < sblock.s_ninodes; i++) {
        int byte_index = i / 8;
        int bit_index = i % 8;
        if ((inode_bitmap[byte_index] & (1 << (7 - bit_index))) == 0) {
            inode_bitmap[byte_index] |= (1 << (7 - bit_index));
            for (int j = 0; j < sblock.s_imap_blocks; j++) {
                int logical_block_num = 2 + j;
                int physical_block_num = 2 + logical_block_num;
                long offset = (physical_block_num - 1) * PHYSICAL_BLOCK_SIZE;
                memcpy(logical_block, &inode_bitmap[j * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
            }
            return i + 1;
        }
    }
    return 0;
}
//从区域位图中分配一个空闲的数据块并返回其编号
unsigned short allocate_zone() {
    for (int i = 0; i < sblock.s_nzones - sblock.s_firstdatazone; i++) {
        int byte_index = i / 8;
        int bit_index = i % 8;
        if ((zone_bitmap[byte_index] & (1 << (7 - bit_index))) == 0) {
            zone_bitmap[byte_index] |= (1 << (7 - bit_index));
            for (int j = 0; j < sblock.s_zmap_blocks; j++) {
                get_partition_logical_block(1 + 1 + sblock.s_imap_blocks + j);
                memcpy(logical_block, &zone_bitmap[j * LOGICAL_BLOCK_SIZE], LOGICAL_BLOCK_SIZE);
                long offset = (1 + 1 + sblock.s_imap_blocks + j) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
            }
            return sblock.s_firstdatazone + i;
        }
    }
    return 0;
}
//将指定的inode结构写入磁盘的inode表中
void write_inode(unsigned short inode_id, struct d_inode* inode) {
    int inode_blocknum = 1 + 1 + sblock.s_imap_blocks + sblock.s_zmap_blocks + (inode_id - 1) / (LOGICAL_BLOCK_SIZE / sizeof(struct d_inode));
    get_partition_logical_block(inode_blocknum);
    memcpy(&logical_block[((inode_id - 1) % (LOGICAL_BLOCK_SIZE / sizeof(struct d_inode))) * sizeof(struct d_inode)], inode, sizeof(struct d_inode));
    long offset = (1 + inode_blocknum) * LOGICAL_BLOCK_SIZE;
    fseek(fd, offset, SEEK_SET);
    fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
}
//在指定路径创建新目录，设置.和..目录项，并更新父目录
void create_directory(const char* path) {
    char parent_path[256], dir_name[NAME_LEN];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) {
        printf("无效路径: %s\n", path);
        return;
    }
    strncpy(dir_name, last_slash + 1, NAME_LEN);
    dir_name[NAME_LEN - 1] = '\0';
    *last_slash = '\0';
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0) {
        printf("父目录不存在: %s\n", parent_path);
        return;
    }
    if (find_inode_by_path(path) != 0) {
        printf("目录已存在: %s\n", path);
        return;
    }
    unsigned short new_inode_id = allocate_inode();
    if (new_inode_id == 0) {
        printf("没有空闲 i 节点\n");
        return;
    }
    struct d_inode new_inode;
    memset(&new_inode, 0, sizeof(new_inode));
    new_inode.i_mode = (4 << 12) | 0755;
    new_inode.i_uid = 0;
    new_inode.i_gid = 0;
    new_inode.i_nlinks = 2;
    new_inode.i_time = time(NULL);
    unsigned short data_block = allocate_zone();
    if (data_block == 0) {
        printf("没有空闲数据块\n");
        return;
    }
    new_inode.i_zone[0] = data_block;
    write_inode(new_inode_id, &new_inode);
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);
    int found = 0;
    for (int i = 0; i < 7 && !found; i++) {
        if (parent_inode.i_zone[i] == 0) {
            parent_inode.i_zone[i] = allocate_zone();
            if (parent_inode.i_zone[i] == 0) {
                printf("父目录无法分配数据块\n");
                return;
            }
            write_inode(parent_inode_id, &parent_inode);
        }
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == 0) {
                dir.inode = new_inode_id;
                strncpy(dir.name, dir_name, NAME_LEN);
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
    }
    if (!found) {
        printf("父目录没有空闲目录项\n");
        return;
    }
    struct dir_entry dot = {new_inode_id, "."};
    struct dir_entry dotdot = {parent_inode_id, ".."};
    get_partition_logical_block(data_block);
    memcpy(&logical_block[0], &dot, sizeof(struct dir_entry));
    memcpy(&logical_block[sizeof(struct dir_entry)], &dotdot, sizeof(struct dir_entry));
    long offset = (1 + data_block) * LOGICAL_BLOCK_SIZE;
    fseek(fd, offset, SEEK_SET);
    fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    printf("目录已创建: %s\n", path);
}
//在指定路径创建新文件，分配所需数据块并初始化内容为0
void create_file(const char* path, unsigned long size) {
    char parent_path[256], file_name[NAME_LEN];
    strcpy(parent_path, path);
    char* last_slash = strrchr(parent_path, '/');
    if (last_slash == NULL || last_slash == path) {
        printf("无效路径: %s\n", path);
        return;
    }
    strncpy(file_name, last_slash + 1, NAME_LEN);
    file_name[NAME_LEN - 1] = '\0';
    *last_slash = '\0';
    unsigned short parent_inode_id = find_inode_by_path(parent_path);
    if (parent_inode_id == 0) {
        printf("父目录不存在: %s\n", parent_path);
        return;
    }
    if (find_inode_by_path(path) != 0) {
        printf("文件已存在: %s\n", path);
        return;
    }
    unsigned short new_inode_id = allocate_inode();
    if (new_inode_id == 0) {
        printf("没有空闲 i 节点\n");
        return;
    }
    struct d_inode new_inode;
    memset(&new_inode, 0, sizeof(new_inode));
    new_inode.i_mode = (8 << 12) | 0644;
    new_inode.i_uid = 0;
    new_inode.i_gid = 0;
    new_inode.i_nlinks = 1;
    new_inode.i_time = time(NULL);
    new_inode.i_size = size;
    int blocks_needed = (size + LOGICAL_BLOCK_SIZE - 1) / LOGICAL_BLOCK_SIZE;
    if (blocks_needed > 7) {
        printf("文件大小超出直接块支持范围: %lu\n", size);
        return;
    }
    for (int i = 0; i < blocks_needed; i++) {
        unsigned short data_block = allocate_zone();
        if (data_block == 0) {
            printf("没有空闲数据块\n");
            return;
        }
        new_inode.i_zone[i] = data_block;
        memset(logical_block, 0, LOGICAL_BLOCK_SIZE);
        long offset = (1 + data_block) * LOGICAL_BLOCK_SIZE;
        fseek(fd, offset, SEEK_SET);
        fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
    }
    write_inode(new_inode_id, &new_inode);
    struct d_inode parent_inode;
    get_inode(parent_inode_id, &parent_inode);
    int found = 0;
    for (int i = 0; i < 7 && !found; i++) {
        if (parent_inode.i_zone[i] == 0) {
            parent_inode.i_zone[i] = allocate_zone();
            if (parent_inode.i_zone[i] == 0) {
                printf("父目录无法分配数据块\n");
                return;
            }
            write_inode(parent_inode_id, &parent_inode);
        }
        get_partition_logical_block(parent_inode.i_zone[i]);
        for (int j = 0; j < LOGICAL_BLOCK_SIZE / sizeof(struct dir_entry); j++) {
            struct dir_entry dir;
            memcpy(&dir, &logical_block[j * sizeof(struct dir_entry)], sizeof(struct dir_entry));
            if (dir.inode == 0) {
                dir.inode = new_inode_id;
                strncpy(dir.name, file_name, NAME_LEN);
                memcpy(&logical_block[j * sizeof(struct dir_entry)], &dir, sizeof(struct dir_entry));
                long offset = (1 + parent_inode.i_zone[i]) * LOGICAL_BLOCK_SIZE;
                fseek(fd, offset, SEEK_SET);
                fwrite(logical_block, LOGICAL_BLOCK_SIZE, 1, fd);
                found = 1;
                break;
            }
        }
    }
    if (!found) {
        printf("父目录没有空闲目录项\n");
        return;
    }
    printf("文件已创建: %s, 大小: %lu\n", path, size);
}
int main(int argc, char* argv[])
{
	int bit;
	struct d_inode* inode = (struct d_inode*)malloc(sizeof(struct d_inode));
	
	char* path = "D:\\minix\\harddisk.img";
	fd = fopen(path, "rb+");
	if(fd==NULL)
		printf("open file failed!\n");
		
	get_partition_table();
	get_super_block();

	load_inode_bitmap();
	load_zone_bitmap();
    print_df();
    print_file_content("/usr/root/hello.c");
    print_large_file_content("/usr/src/linux/mm/memory.c");
    delete_file("/usr/root/hello.c");
    delete_directory("/usr/root/shoe");
    create_directory("/usr/root/dir");
    create_file("/usr/root/file.txt", 10240);
	//i节点位图的第一位对应文件系统的根节点
	//如果第一位为1，则打印根节点
	bit = is_inode_valid(1);
	if(bit == 1)
		print_inode(1, -1, "\\");
	else
		printf("root node lost！\n");

	return 0;
}
