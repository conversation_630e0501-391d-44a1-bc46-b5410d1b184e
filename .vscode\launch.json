{"version": "0.2.0", "configurations": [{"name": "(gdb) Launch", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/minix.exe", "targetArchitecture": "x86", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "preLaunchTask": "生成项目(make)", "MIMode": "gdb", "miDebuggerPath": "${execPath}.toolchain\\bin\\gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}